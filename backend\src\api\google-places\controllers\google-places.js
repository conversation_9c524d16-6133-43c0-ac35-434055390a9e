'use strict';

/**
 * Google Places controller
 */

module.exports = {
  /**
   * Find nearby places using Google Places API
   */
  async nearby(ctx) {
    try {
      const { lat, lng, types, radius = 1500, maxResults = 10 } = ctx.request.body;

      // Validate required parameters
      if (!lat || !lng) {
        return ctx.badRequest('Latitude and longitude are required');
      }

      if (!types || !Array.isArray(types) || types.length === 0) {
        return ctx.badRequest('At least one place type is required');
      }

      // Use the Google Places service
      const googlePlacesService = strapi.service('api::property.google-places');
      
      const places = await googlePlacesService.findNearbyPlaces({
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        types,
        radius: parseInt(radius),
        maxResults: parseInt(maxResults)
      });

      ctx.send({
        success: true,
        places,
        count: places.length
      });

    } catch (error) {
      console.error('Google Places API error:', error);
      ctx.send({
        success: false,
        error: error.message,
        places: []
      });
    }
  }
};
