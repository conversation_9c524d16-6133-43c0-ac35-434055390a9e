'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import { propertiesAPI } from '@/lib/api';
import { NearbyPlaces } from '@/components/NearbyPlaces';
import { 
  MapPin, Bed, Bath, Square, Eye, Calendar, User, Phone, Mail, 
  ArrowLeft, Heart, Share2, Star, Car, Home, Wifi, Shield,
  ChevronLeft, ChevronRight, X
} from 'lucide-react';

interface Property {
  documentId: string;
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  propertyType: string;
  status: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  address: string;
  city: string;
  country: string;
  neighborhood?: string;
  coordinates?: { lat: number; lng: number };
  nearbyPlaces?: any;
  propertyCode?: string;
  isLuxury?: boolean;
  features?: string[];
  views: number;
  images?: any[];
  floorPlan?: any[];
  virtualTour?: string;
  yearBuilt?: number;
  parking?: number;
  furnished?: boolean;
  petFriendly?: boolean;
  owner?: any;
  agent?: any;
  createdAt: string;
  updatedAt: string;
  slug?: string;
}

const PropertyDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [nearbyPlaces, setNearbyPlaces] = useState<any>(null);
  const [loadingNearbyPlaces, setLoadingNearbyPlaces] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchProperty(params.id as string);
    }
  }, [params.id]);

  const fetchProperty = async (id: string) => {
    try {
      setLoading(true);
      const response = await propertiesAPI.getById(id, {
        populate: ['images', 'floorPlan', 'owner', 'agent'],
      });
      setProperty(response.data);

      // Fetch nearby places if they exist
      if (response.data.nearbyPlaces) {
        setNearbyPlaces(response.data.nearbyPlaces);
      }
    } catch (err: any) {
      setError('Failed to fetch property details');
      console.error('Error fetching property:', err);
    } finally {
      setLoading(false);
    }
  };

  const generateNearbyPlaces = async () => {
    if (!property?.id || !property.coordinates) {
      alert('Property must have coordinates to generate nearby places');
      return;
    }

    setLoadingNearbyPlaces(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/properties/${property.id}/generate-nearby-places`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setNearbyPlaces(data.data.nearbyPlaces);
      } else {
        const errorData = await response.json();
        alert(`Failed to generate nearby places: ${errorData.error?.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error generating nearby places:', error);
      alert('Failed to generate nearby places');
    } finally {
      setLoadingNearbyPlaces(false);
    }
  };

  const getImageUrl = (image: any) => {
    if (image?.url) {
      return image.url.startsWith('http') ? image.url : `http://localhost:1337${image.url}`;
    }
    return '/api/placeholder/800/600';
  };

  const formatPrice = (price: number, currency: string) => {
    return `${currency} ${price.toLocaleString()}`;
  };

  const nextImage = () => {
    if (property?.images && property.images.length > 0) {
      setCurrentImageIndex((prev) => 
        prev === property.images!.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    if (property?.images && property.images.length > 0) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? property.images!.length - 1 : prev - 1
      );
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: property?.title,
          text: property?.description,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-32 mb-6"></div>
              <div className="h-96 bg-gray-300 rounded-lg mb-8"></div>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2 space-y-6">
                  <div className="h-8 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded"></div>
                    <div className="h-4 bg-gray-300 rounded"></div>
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  </div>
                </div>
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="h-8 bg-gray-300 rounded mb-4"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-300 rounded"></div>
                    <div className="h-4 bg-gray-300 rounded"></div>
                    <div className="h-10 bg-gray-300 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !property) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Property Not Found</h1>
            <p className="text-gray-600 mb-6">{error || 'The property you are looking for does not exist.'}</p>
            <button
              onClick={() => router.push('/properties')}
              className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors"
            >
              Back to Properties
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Back Button */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Properties
            </button>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Image Gallery */}
          <div className="relative mb-8">
            <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden">
              <img
                src={getImageUrl(property.images?.[currentImageIndex])}
                alt={property.title}
                className="w-full h-full object-cover cursor-pointer"
                onClick={() => setShowImageModal(true)}
              />
              
              {property.images && property.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  >
                    <ChevronLeft className="h-6 w-6" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
                  >
                    <ChevronRight className="h-6 w-6" />
                  </button>
                  
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {property.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all ${
                          index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                        }`}
                      />
                    ))}
                  </div>
                </>
              )}
              
              {/* Status and Luxury Badges */}
              <div className="absolute top-4 left-4 flex gap-2">
                <span className={`text-white px-3 py-1 rounded-full text-sm font-semibold capitalize ${
                  property.status === 'for-sale' ? 'bg-green-600' :
                  property.status === 'for-rent' ? 'bg-blue-600' :
                  property.status === 'sold' ? 'bg-gray-600' :
                  property.status === 'rented' ? 'bg-purple-600' :
                  'bg-orange-600'
                }`}>
                  {property.status.replace('-', ' ')}
                </span>
                {property.isLuxury && (
                  <span className="bg-yellow-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                    <Star className="h-3 w-3 mr-1" />
                    Luxury
                  </span>
                )}
              </div>
              
              {/* Action Buttons */}
              <div className="absolute top-4 right-4 flex gap-2">
                <button
                  onClick={() => setIsFavorite(!isFavorite)}
                  className={`p-2 rounded-full transition-all ${
                    isFavorite ? 'bg-red-600 text-white' : 'bg-white bg-opacity-90 text-gray-700 hover:bg-opacity-100'
                  }`}
                >
                  <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />
                </button>
                <button
                  onClick={handleShare}
                  className="p-2 rounded-full bg-white bg-opacity-90 text-gray-700 hover:bg-opacity-100 transition-all"
                >
                  <Share2 className="h-5 w-5" />
                </button>
              </div>
              
              {/* Views Counter */}
              <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full flex items-center">
                <Eye className="h-4 w-4 mr-1" />
                <span className="text-sm">{property.views || 0} views</span>
              </div>
            </div>
            
            {/* Thumbnail Strip */}
            {property.images && property.images.length > 1 && (
              <div className="flex space-x-2 mt-4 overflow-x-auto pb-2">
                {property.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${
                      index === currentImageIndex ? 'border-blue-600' : 'border-transparent'
                    }`}
                  >
                    <img
                      src={getImageUrl(image)}
                      alt={`${property.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Property Details */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Property Info */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">{property.title}</h1>
                    <div className="flex items-center text-gray-600 mb-2">
                      <MapPin className="h-5 w-5 mr-2" />
                      <span>
                        {property.address}
                        {property.neighborhood && `, ${property.neighborhood}`}
                        , {property.city}, {property.country}
                      </span>
                    </div>
                    {property.propertyCode && (
                      <div className="text-sm text-gray-500">
                        Property Code: <span className="font-semibold">#{property.propertyCode}</span>
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-blue-600 mb-1">
                      {formatPrice(property.price, property.currency)}
                    </div>
                    <div className="text-sm text-gray-500 capitalize">
                      {property.propertyType.replace('-', ' ')}
                    </div>
                  </div>
                </div>

                {/* Property Features */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 py-6 border-t border-b border-gray-200">
                  {property.bedrooms > 0 && (
                    <div className="text-center">
                      <Bed className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{property.bedrooms}</div>
                      <div className="text-sm text-gray-600">Bedrooms</div>
                    </div>
                  )}
                  {property.bathrooms > 0 && (
                    <div className="text-center">
                      <Bath className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{property.bathrooms}</div>
                      <div className="text-sm text-gray-600">Bathrooms</div>
                    </div>
                  )}
                  {property.area > 0 && (
                    <div className="text-center">
                      <Square className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{property.area}</div>
                      <div className="text-sm text-gray-600">{property.areaUnit || 'sq ft'}</div>
                    </div>
                  )}
                  {property.parking && property.parking > 0 && (
                    <div className="text-center">
                      <Car className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-gray-900">{property.parking}</div>
                      <div className="text-sm text-gray-600">Parking</div>
                    </div>
                  )}
                </div>

                {/* Description */}
                {property.description && (
                  <div className="pt-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">Description</h3>
                    <p className="text-gray-600 leading-relaxed whitespace-pre-line">
                      {property.description}
                    </p>
                  </div>
                )}
              </div>

              {/* Features & Amenities */}
              {property.features && property.features.length > 0 && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Features & Amenities</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {property.features.map((feature, index) => (
                      <div key={index} className="flex items-center text-gray-600">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                        <span className="capitalize">{feature.replace('-', ' ')}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Additional Details */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Property Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {property.yearBuilt && (
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Year Built</span>
                      <span className="font-semibold">{property.yearBuilt}</span>
                    </div>
                  )}
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Property Type</span>
                    <span className="font-semibold capitalize">{property.propertyType.replace('-', ' ')}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Status</span>
                    <span className="font-semibold capitalize">{property.status.replace('-', ' ')}</span>
                  </div>
                  {property.furnished !== undefined && (
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Furnished</span>
                      <span className="font-semibold">{property.furnished ? 'Yes' : 'No'}</span>
                    </div>
                  )}
                  {property.petFriendly !== undefined && (
                    <div className="flex justify-between py-2 border-b border-gray-100">
                      <span className="text-gray-600">Pet Friendly</span>
                      <span className="font-semibold">{property.petFriendly ? 'Yes' : 'No'}</span>
                    </div>
                  )}
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Listed</span>
                    <span className="font-semibold">{new Date(property.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contact Card */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Contact Agent</h3>

                {property.agent ? (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">{property.agent.name || 'Real Estate Agent'}</div>
                        <div className="text-sm text-gray-600">{property.agent.title || 'Licensed Agent'}</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      {property.agent.phone && (
                        <a
                          href={`tel:${property.agent.phone}`}
                          className="flex items-center w-full bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 transition-colors"
                        >
                          <Phone className="h-5 w-5 mr-3" />
                          Call Agent
                        </a>
                      )}
                      {property.agent.email && (
                        <a
                          href={`mailto:${property.agent.email}?subject=Inquiry about ${property.title}`}
                          className="flex items-center w-full bg-gray-100 text-gray-900 px-4 py-3 rounded-md hover:bg-gray-200 transition-colors"
                        >
                          <Mail className="h-5 w-5 mr-3" />
                          Send Email
                        </a>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900">Contact Us</div>
                        <div className="text-sm text-gray-600">For more information</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <button className="flex items-center w-full bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 transition-colors">
                        <Phone className="h-5 w-5 mr-3" />
                        Request Info
                      </button>
                      <button className="flex items-center w-full bg-gray-100 text-gray-900 px-4 py-3 rounded-md hover:bg-gray-200 transition-colors">
                        <Mail className="h-5 w-5 mr-3" />
                        Schedule Viewing
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Quick Stats */}
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Property ID</span>
                    <span className="font-semibold">#{property.propertyCode || property.documentId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Views</span>
                    <span className="font-semibold">{property.views || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Listed</span>
                    <span className="font-semibold">{new Date(property.createdAt).toLocaleDateString()}</span>
                  </div>
                  {property.updatedAt !== property.createdAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Updated</span>
                      <span className="font-semibold">{new Date(property.updatedAt).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Nearby Places Section */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">What's Nearby</h2>
              {property.coordinates && (
                <button
                  onClick={generateNearbyPlaces}
                  disabled={loadingNearbyPlaces}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingNearbyPlaces ? 'Generating...' : nearbyPlaces ? 'Refresh Places' : 'Generate Nearby Places'}
                </button>
              )}
            </div>

            {!property.coordinates ? (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  <MapPin className="h-12 w-12 mx-auto" />
                </div>
                <p className="text-gray-600">Property coordinates are required to show nearby places</p>
              </div>
            ) : nearbyPlaces ? (
              <NearbyPlaces nearbyPlaces={nearbyPlaces} showHeader={false} />
            ) : (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  <MapPin className="h-12 w-12 mx-auto" />
                </div>
                <p className="text-gray-600 mb-4">Discover restaurants, schools, shopping, and more around this property</p>
                <button
                  onClick={generateNearbyPlaces}
                  disabled={loadingNearbyPlaces}
                  className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loadingNearbyPlaces ? 'Generating...' : 'Generate Nearby Places'}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Image Modal */}
        {showImageModal && property.images && property.images.length > 0 && (
          <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
            <div className="relative max-w-4xl max-h-full p-4">
              <button
                onClick={() => setShowImageModal(false)}
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
              >
                <X className="h-8 w-8" />
              </button>

              <img
                src={getImageUrl(property.images[currentImageIndex])}
                alt={property.title}
                className="max-w-full max-h-full object-contain"
              />

              {property.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
                  >
                    <ChevronLeft className="h-12 w-12" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
                  >
                    <ChevronRight className="h-12 w-12" />
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default PropertyDetailPage;
