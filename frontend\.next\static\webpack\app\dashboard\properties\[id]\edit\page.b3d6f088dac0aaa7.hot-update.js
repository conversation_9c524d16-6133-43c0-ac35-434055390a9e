"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/properties/[id]/edit/page.tsx":
/*!*********************************************************!*\
  !*** ./src/app/dashboard/properties/[id]/edit/page.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/Dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_SimpleNeighborhoodInput__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SimpleNeighborhoodInput */ \"(app-pages-browser)/./src/components/SimpleNeighborhoodInput.tsx\");\n/* harmony import */ var _components_CoordinateSelector__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/CoordinateSelector */ \"(app-pages-browser)/./src/components/CoordinateSelector.tsx\");\n/* harmony import */ var _components_MapPreviewWithNearbyPlaces__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MapPreviewWithNearbyPlaces */ \"(app-pages-browser)/./src/components/MapPreviewWithNearbyPlaces.tsx\");\n/* harmony import */ var _components_ImageUploadWithDragDrop__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ImageUploadWithDragDrop */ \"(app-pages-browser)/./src/components/ImageUploadWithDragDrop.tsx\");\n/* harmony import */ var _components_FloorPlanUploadWithDragDrop__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FloorPlanUploadWithDragDrop */ \"(app-pages-browser)/./src/components/FloorPlanUploadWithDragDrop.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_11__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst EditPropertyPage = ()=>{\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const { user, isAuthenticated, authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        price: 0,\n        currency: 'USD',\n        propertyType: 'apartment',\n        status: 'for-sale',\n        bedrooms: 1,\n        bathrooms: 1,\n        area: 0,\n        areaUnit: 'sqm',\n        address: '',\n        city: '',\n        country: '',\n        neighborhood: [],\n        coordinates: undefined,\n        propertyCode: '',\n        isLuxury: false,\n        features: [],\n        yearBuilt: undefined,\n        parking: undefined,\n        furnished: false,\n        petFriendly: false,\n        virtualTour: '',\n        images: null,\n        floorPlan: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPropertyPage.useEffect\": ()=>{\n            if (!authLoading && !isAuthenticated) {\n                router.push('/auth/login?redirect=/dashboard/properties');\n            }\n        }\n    }[\"EditPropertyPage.useEffect\"], [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPropertyPage.useEffect\": ()=>{\n            const fetchProperty = {\n                \"EditPropertyPage.useEffect.fetchProperty\": async ()=>{\n                    if (!params.id) return;\n                    try {\n                        const property = await _lib_api__WEBPACK_IMPORTED_MODULE_5__.propertiesAPI.getForEdit(params.id);\n                        console.log('Loaded property data:', property);\n                        // Normalize neighborhood data\n                        let neighborhoods = [];\n                        if (property.neighborhood) {\n                            if (typeof property.neighborhood === 'string') {\n                                neighborhoods = [\n                                    property.neighborhood\n                                ];\n                            } else if (Array.isArray(property.neighborhood)) {\n                                if (property.neighborhood.length > 0 && typeof property.neighborhood[0] === 'string') {\n                                    neighborhoods = property.neighborhood;\n                                } else {\n                                    // Convert object format to string array\n                                    neighborhoods = property.neighborhood.map({\n                                        \"EditPropertyPage.useEffect.fetchProperty\": (n)=>n.name || n\n                                    }[\"EditPropertyPage.useEffect.fetchProperty\"]);\n                                }\n                            }\n                        }\n                        const formDataToSet = {\n                            title: property.title || '',\n                            description: property.description || '',\n                            price: Number(property.price) || 0,\n                            currency: property.currency || 'USD',\n                            propertyType: property.propertyType || 'apartment',\n                            status: property.status || 'for-sale',\n                            bedrooms: Number(property.bedrooms) || 1,\n                            bathrooms: Number(property.bathrooms) || 1,\n                            area: Number(property.area) || 0,\n                            areaUnit: property.areaUnit || 'sqm',\n                            address: property.address || '',\n                            city: property.city || '',\n                            country: property.country || '',\n                            neighborhood: neighborhoods,\n                            coordinates: property.coordinates,\n                            propertyCode: property.propertyCode || '',\n                            isLuxury: Boolean(property.isLuxury),\n                            features: Array.isArray(property.features) ? property.features : [],\n                            yearBuilt: property.yearBuilt ? Number(property.yearBuilt) : undefined,\n                            parking: property.parking ? Number(property.parking) : undefined,\n                            furnished: Boolean(property.furnished),\n                            petFriendly: Boolean(property.petFriendly),\n                            virtualTour: property.virtualTour || '',\n                            images: null,\n                            floorPlan: null\n                        };\n                        console.log('Setting form data:', formDataToSet);\n                        setFormData(formDataToSet);\n                    } catch (err) {\n                        var _err_response_data_error, _err_response_data, _err_response;\n                        setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : (_err_response_data_error = _err_response_data.error) === null || _err_response_data_error === void 0 ? void 0 : _err_response_data_error.message) || 'Failed to load property');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditPropertyPage.useEffect.fetchProperty\"];\n            if (user) {\n                fetchProperty();\n            }\n        }\n    }[\"EditPropertyPage.useEffect\"], [\n        params.id,\n        user\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value, type } = e.target;\n        if (type === 'checkbox') {\n            const checked = e.target.checked;\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: checked\n                }));\n        } else if (type === 'number') {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: parseFloat(value) || 0\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    };\n    const handleFeatureToggle = (feature)=>{\n        setFormData((prev)=>({\n                ...prev,\n                features: prev.features.includes(feature) ? prev.features.filter((f)=>f !== feature) : [\n                    ...prev.features,\n                    feature\n                ]\n            }));\n    };\n    const handleImageUpload = (e)=>{\n        const files = e.target.files;\n        setFormData((prev)=>({\n                ...prev,\n                images: files\n            }));\n    };\n    const handleFloorPlanChange = (file)=>{\n        setFormData((prev)=>({\n                ...prev,\n                floorPlan: file\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSaving(true);\n        setError(null);\n        try {\n            // Create FormData for file uploads\n            const formDataToSend = new FormData();\n            // Add property data\n            const propertyData = {\n                title: formData.title,\n                description: formData.description,\n                price: formData.price,\n                currency: formData.currency,\n                propertyType: formData.propertyType,\n                status: formData.status,\n                bedrooms: formData.bedrooms,\n                bathrooms: formData.bathrooms,\n                area: formData.area,\n                areaUnit: formData.areaUnit,\n                address: formData.address,\n                city: formData.city,\n                country: formData.country,\n                neighborhood: formData.neighborhood,\n                coordinates: formData.coordinates,\n                propertyCode: formData.propertyCode,\n                isLuxury: formData.isLuxury,\n                features: formData.features,\n                yearBuilt: formData.yearBuilt,\n                parking: formData.parking,\n                furnished: formData.furnished,\n                petFriendly: formData.petFriendly,\n                virtualTour: formData.virtualTour\n            };\n            formDataToSend.append('data', JSON.stringify(propertyData));\n            // Add images if selected\n            if (formData.images && formData.images.length > 0) {\n                for(let i = 0; i < formData.images.length; i++){\n                    formDataToSend.append('files.images', formData.images[i]);\n                }\n            }\n            // Add floor plan if selected\n            if (formData.floorPlan) {\n                formDataToSend.append('files.floorPlan', formData.floorPlan);\n            }\n            // Submit to API\n            const response = await fetch(\"\".concat(\"http://localhost:1337\", \"/api/properties/\").concat(params.id), {\n                method: 'PUT',\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('jwt'))\n                },\n                body: formDataToSend\n            });\n            if (!response.ok) {\n                var _errorData_error;\n                const errorData = await response.json();\n                throw new Error(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || 'Failed to update property');\n            }\n            setSuccess(true);\n            setTimeout(()=>{\n                router.push('/dashboard/properties');\n            }, 2000);\n        } catch (err) {\n            setError(err.message || 'Failed to update property');\n        } finally{\n            setSaving(false);\n        }\n    };\n    const propertyTypes = [\n        'apartment',\n        'villa',\n        'townhouse',\n        'penthouse',\n        'studio',\n        'duplex',\n        'land',\n        'commercial'\n    ];\n    const statusOptions = [\n        'for-sale',\n        'for-rent',\n        'sold',\n        'rented',\n        'off-market'\n    ];\n    const availableFeatures = [\n        'Swimming Pool',\n        'Gym',\n        'Garden',\n        'Balcony',\n        'Terrace',\n        'Garage',\n        'Security',\n        'Elevator',\n        'Air Conditioning',\n        'Heating',\n        'Fireplace',\n        'Walk-in Closet',\n        'Storage Room',\n        'Laundry Room',\n        'Maid Room'\n    ];\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error && !formData.title) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                            href: \"/dashboard/properties\",\n                            className: \"inline-flex items-center mt-3 text-blue-600 hover:text-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Back to Properties\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 284,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                    href: \"/dashboard/properties\",\n                                    className: \"inline-flex items-center text-gray-600 hover:text-gray-900\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Back to Properties\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Edit Property\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        formData.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-700 mt-1\",\n                                            children: [\n                                                formData.title,\n                                                formData.propertyCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-2\",\n                                                    children: [\n                                                        \"(ID: \",\n                                                        formData.propertyCode,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mt-1\",\n                                            children: \"Update your property listing details\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                href: \"/properties/\".concat(params.id),\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Preview\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, undefined),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-md p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-600\",\n                        children: \"Property updated successfully! Redirecting...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 11\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                    children: \"Basic Information\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Property Title *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"title\",\n                                                    value: formData.title,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"e.g., Luxury 3BR Apartment in Downtown\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleInputChange,\n                                                    rows: 4,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"Describe your property...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                    children: \"Price & Details\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Price *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    name: \"price\",\n                                                    value: formData.price,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    min: \"0\",\n                                                    step: \"0.01\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Currency\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"currency\",\n                                                    value: formData.currency,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"USD\",\n                                                            children: \"USD\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"EUR\",\n                                                            children: \"EUR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"GBP\",\n                                                            children: \"GBP\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"AED\",\n                                                            children: \"AED\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"SAR\",\n                                                            children: \"SAR\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Property Type *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"propertyType\",\n                                                    value: formData.propertyType,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    children: propertyTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type,\n                                                            children: type.charAt(0).toUpperCase() + type.slice(1)\n                                                        }, type, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"status\",\n                                                    value: formData.status,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    children: statusOptions.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: status,\n                                                            children: status.replace('-', ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase())\n                                                        }, status, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Bedrooms\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    name: \"bedrooms\",\n                                                    value: formData.bedrooms,\n                                                    onChange: handleInputChange,\n                                                    min: \"0\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Bathrooms\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    name: \"bathrooms\",\n                                                    value: formData.bathrooms,\n                                                    onChange: handleInputChange,\n                                                    min: \"0\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Area *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    name: \"area\",\n                                                    value: formData.area,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    min: \"0\",\n                                                    step: \"0.01\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Area Unit\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    name: \"areaUnit\",\n                                                    value: formData.areaUnit,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"sqm\",\n                                                            children: \"Square Meters\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"sqft\",\n                                                            children: \"Square Feet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Year Built\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    name: \"yearBuilt\",\n                                                    value: formData.yearBuilt || '',\n                                                    onChange: handleInputChange,\n                                                    min: \"1800\",\n                                                    max: new Date().getFullYear(),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Parking Spaces\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    name: \"parking\",\n                                                    value: formData.parking || '',\n                                                    onChange: handleInputChange,\n                                                    min: \"0\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Virtual Tour URL\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"url\",\n                                                    name: \"virtualTour\",\n                                                    value: formData.virtualTour,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"https://...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                    children: \"Location\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Address *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"address\",\n                                                    value: formData.address,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"City *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"city\",\n                                                    value: formData.city,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Country *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"country\",\n                                                    value: formData.country,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleNeighborhoodInput__WEBPACK_IMPORTED_MODULE_6__.SimpleNeighborhoodInput, {\n                                                value: formData.neighborhood,\n                                                onChange: (neighborhoods)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            neighborhood: neighborhoods\n                                                        })),\n                                                maxSelections: 3,\n                                                placeholder: \"Enter neighborhood name...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CoordinateSelector__WEBPACK_IMPORTED_MODULE_7__.CoordinateSelector, {\n                                                value: formData.coordinates,\n                                                onChange: (coordinates)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            coordinates\n                                                        })),\n                                                address: \"\".concat(formData.address, \", \").concat(formData.city, \", \").concat(formData.country).trim().replace(/^,|,$/, '')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MapPreviewWithNearbyPlaces__WEBPACK_IMPORTED_MODULE_8__.MapPreviewWithNearbyPlaces, {\n                                                coordinates: formData.coordinates,\n                                                address: \"\".concat(formData.address, \", \").concat(formData.city, \", \").concat(formData.country).trim().replace(/^,|,$/, ''),\n                                                className: \"mt-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Property Code\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"propertyCode\",\n                                                    value: formData.propertyCode,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"e.g., PROP-001\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                    children: \"Features & Amenities\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                    children: availableFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center space-x-2 cursor-pointer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: formData.features.includes(feature),\n                                                    onChange: ()=>handleFeatureToggle(feature),\n                                                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, feature, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-base font-semibold text-gray-900 mb-4\",\n                                            children: \"Additional Info\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-2 cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"isLuxury\",\n                                                            checked: formData.isLuxury,\n                                                            onChange: handleInputChange,\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Luxury Property\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-2 cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"furnished\",\n                                                            checked: formData.furnished,\n                                                            onChange: handleInputChange,\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Furnished\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 709,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center space-x-2 cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            name: \"petFriendly\",\n                                                            checked: formData.petFriendly,\n                                                            onChange: handleInputChange,\n                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700\",\n                                                            children: \"Pet Friendly\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2\",\n                                    children: \"Media Upload\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUploadWithDragDrop__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    onImagesChange: (files)=>setFormData((prev)=>({\n                                                ...prev,\n                                                images: files\n                                            })),\n                                    maxImages: 15,\n                                    className: \"w-full mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloorPlanUploadWithDragDrop__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    onFloorPlanChange: handleFloorPlanChange,\n                                    currentFloorPlan: formData.floorPlan,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end space-x-4 pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_11___default()), {\n                                    href: \"/dashboard/properties\",\n                                    className: \"px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: saving,\n                                    className: \"inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Saving...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Save Changes\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                            lineNumber: 748,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\edit\\\\page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EditPropertyPage, \"WI1Cl6tzoBBapsz184LisMdtw0s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = EditPropertyPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EditPropertyPage);\nvar _c;\n$RefreshReg$(_c, \"EditPropertyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/properties/[id]/edit/page.tsx\n"));

/***/ })

});