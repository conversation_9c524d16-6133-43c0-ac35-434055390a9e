globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/properties/[id]/edit/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/Providers.tsx":{"*":{"id":"(ssr)/./src/components/Providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/properties/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Home/FeaturedProjects.tsx":{"*":{"id":"(ssr)/./src/components/Home/FeaturedProjects.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Home/FeaturedProperties.tsx":{"*":{"id":"(ssr)/./src/components/Home/FeaturedProperties.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Home/Hero.tsx":{"*":{"id":"(ssr)/./src/components/Home/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layout/Layout.tsx":{"*":{"id":"(ssr)/./src/components/Layout/Layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/properties/page.tsx":{"*":{"id":"(ssr)/./src/app/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/properties/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/properties/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/properties/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/properties/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\components\\Providers.tsx":{"id":"(app-pages-browser)/./src/components/Providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\app\\dashboard\\properties\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/properties/page.tsx","name":"*","chunks":["app/dashboard/properties/page","static/chunks/app/dashboard/properties/page.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\components\\Home\\FeaturedProjects.tsx":{"id":"(app-pages-browser)/./src/components/Home/FeaturedProjects.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\components\\Home\\FeaturedProperties.tsx":{"id":"(app-pages-browser)/./src/components/Home/FeaturedProperties.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\components\\Home\\Hero.tsx":{"id":"(app-pages-browser)/./src/components/Home/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\components\\Layout\\Layout.tsx":{"id":"(app-pages-browser)/./src/components/Layout/Layout.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\app\\properties\\page.tsx":{"id":"(app-pages-browser)/./src/app/properties/page.tsx","name":"*","chunks":[],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\app\\dashboard\\properties\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/properties/[id]/edit/page.tsx","name":"*","chunks":["app/dashboard/properties/[id]/edit/page","static/chunks/app/dashboard/properties/%5Bid%5D/edit/page.js"],"async":false},"D:\\3-Development Web\\real estate\\frontend\\src\\app\\properties\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/properties/[id]/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\3-Development Web\\real estate\\frontend\\src\\":[],"D:\\3-Development Web\\real estate\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\3-Development Web\\real estate\\frontend\\src\\app\\page":[],"D:\\3-Development Web\\real estate\\frontend\\src\\app\\dashboard\\page":[],"D:\\3-Development Web\\real estate\\frontend\\src\\app\\dashboard\\properties\\page":[],"D:\\3-Development Web\\real estate\\frontend\\src\\app\\dashboard\\properties\\[id]\\edit\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Providers.tsx":{"*":{"id":"(rsc)/./src/components/Providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/properties/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Home/FeaturedProjects.tsx":{"*":{"id":"(rsc)/./src/components/Home/FeaturedProjects.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Home/FeaturedProperties.tsx":{"*":{"id":"(rsc)/./src/components/Home/FeaturedProperties.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Home/Hero.tsx":{"*":{"id":"(rsc)/./src/components/Home/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layout/Layout.tsx":{"*":{"id":"(rsc)/./src/components/Layout/Layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/properties/page.tsx":{"*":{"id":"(rsc)/./src/app/properties/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/properties/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/properties/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/properties/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/properties/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}