'use client';

import React, { useState, useEffect } from 'react';
import { MapPin, Navigation, Clock, Star, Phone, Globe, ChevronDown, ChevronUp } from 'lucide-react';

interface Coordinates {
  lat: number;
  lng: number;
}

interface NearbyPlace {
  place_id: string;
  name: string;
  vicinity: string;
  rating?: number;
  user_ratings_total?: number;
  price_level?: number;
  opening_hours?: {
    open_now: boolean;
  };
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
  }>;
  types: string[];
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
}

interface PlaceCategory {
  name: string;
  displayName: string;
  places: NearbyPlace[];
  icon: string;
  color: string;
}

interface MapPreviewWithNearbyPlacesProps {
  coordinates?: Coordinates;
  address?: string;
  className?: string;
}

export const MapPreviewWithNearbyPlaces: React.FC<MapPreviewWithNearbyPlacesProps> = ({
  coordinates,
  address,
  className = ''
}) => {
  const [nearbyPlaces, setNearbyPlaces] = useState<PlaceCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (coordinates) {
      fetchNearbyPlaces();
    } else {
      setNearbyPlaces([]);
    }
  }, [coordinates]);

  const fetchNearbyPlaces = async () => {
    if (!coordinates) return;

    setLoading(true);
    setError(null);

    try {
      // Try to get enabled categories first, with fallback
      let enabledCategories = [];

      try {
        const categoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/nearby-place-categories/enabled`);

        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          enabledCategories = categoriesData.data || [];
        }
      } catch (categoryError) {
        console.warn('Categories API not available, using fallback categories');
      }

      // Fallback categories if API is not available
      if (enabledCategories.length === 0) {
        enabledCategories = [
          {
            name: 'education',
            displayName: 'Education',
            googlePlaceTypes: ['school', 'primary_school', 'secondary_school', 'university', 'library'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🎓',
            color: '#3B82F6'
          },
          {
            name: 'restaurants',
            displayName: 'Restaurants & Food',
            googlePlaceTypes: ['restaurant', 'cafe', 'bar', 'bakery', 'meal_delivery', 'meal_takeaway'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🍽️',
            color: '#EF4444'
          },
          {
            name: 'shopping',
            displayName: 'Shopping',
            googlePlaceTypes: ['shopping_mall', 'supermarket', 'convenience_store', 'department_store', 'clothing_store', 'electronics_store', 'store'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🛍️',
            color: '#10B981'
          },
          {
            name: 'healthcare',
            displayName: 'Healthcare',
            googlePlaceTypes: ['hospital', 'doctor', 'dentist', 'pharmacy', 'physiotherapist', 'veterinary_care'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🏥',
            color: '#F59E0B'
          },
          {
            name: 'transportation',
            displayName: 'Transportation',
            googlePlaceTypes: ['bus_station', 'train_station', 'subway_station', 'light_rail_station', 'transit_station', 'taxi_stand', 'airport', 'gas_station'],
            searchRadius: 1000,
            maxResults: 10,
            icon: '🚌',
            color: '#8B5CF6'
          }
        ];
      }

      // Fetch nearby places for each category
      const placePromises = enabledCategories.map(async (category: any) => {
        try {
          const places = await searchNearbyPlaces(coordinates, category.googlePlaceTypes, category.searchRadius, category.maxResults);
          return {
            name: category.name,
            displayName: category.displayName,
            places: places,
            icon: category.icon,
            color: category.color
          };
        } catch (err) {
          console.error(`Failed to fetch places for category ${category.name}:`, err);
          return {
            name: category.name,
            displayName: category.displayName,
            places: [],
            icon: category.icon,
            color: category.color
          };
        }
      });

      const results = await Promise.all(placePromises);
      setNearbyPlaces(results.filter(category => category.places.length > 0));

    } catch (err: any) {
      setError(err.message || 'Failed to fetch nearby places');
    } finally {
      setLoading(false);
    }
  };

  const searchNearbyPlaces = async (
    coords: Coordinates,
    placeTypes: string[],
    radius: number = 1500,
    maxResults: number = 5
  ): Promise<NearbyPlace[]> => {
    // This would typically use Google Places API directly
    // For now, we'll simulate the response
    const mockPlaces: NearbyPlace[] = [
      {
        place_id: 'mock_1',
        name: 'Sample School',
        vicinity: '123 Education St',
        rating: 4.5,
        user_ratings_total: 120,
        opening_hours: { open_now: true },
        types: ['school', 'establishment'],
        geometry: {
          location: {
            lat: coords.lat + 0.001,
            lng: coords.lng + 0.001
          }
        }
      },
      {
        place_id: 'mock_2',
        name: 'Local Restaurant',
        vicinity: '456 Food Ave',
        rating: 4.2,
        user_ratings_total: 89,
        price_level: 2,
        opening_hours: { open_now: false },
        types: ['restaurant', 'food', 'establishment'],
        geometry: {
          location: {
            lat: coords.lat - 0.001,
            lng: coords.lng + 0.001
          }
        }
      }
    ];

    // Filter mock places based on place types
    return mockPlaces.filter(place => 
      place.types.some(type => placeTypes.includes(type))
    ).slice(0, maxResults);
  };

  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const calculateDistance = (place: NearbyPlace): string => {
    if (!coordinates) return '';
    
    const R = 6371; // Earth's radius in km
    const dLat = (place.geometry.location.lat - coordinates.lat) * Math.PI / 180;
    const dLng = (place.geometry.location.lng - coordinates.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(coordinates.lat * Math.PI / 180) * Math.cos(place.geometry.location.lat * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    
    return distance < 1 ? `${Math.round(distance * 1000)}m` : `${distance.toFixed(1)}km`;
  };

  const renderPriceLevel = (level?: number) => {
    if (!level) return null;
    return (
      <span className="text-green-600 font-medium">
        {'$'.repeat(level)}
      </span>
    );
  };

  if (!coordinates) {
    return (
      <div className={`bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center ${className}`}>
        <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <p className="text-gray-600 font-medium">Select coordinates to preview nearby places</p>
        <p className="text-sm text-gray-500 mt-1">
          Use the coordinate selector above to see what's around your property
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
        <div className="flex items-center space-x-2">
          <Navigation className="h-5 w-5" />
          <h3 className="font-semibold">Nearby Places Preview</h3>
        </div>
        <p className="text-blue-100 text-sm mt-1">
          {address || `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`}
        </p>
      </div>

      {/* Content */}
      <div className="p-4">
        {loading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
            <p className="text-gray-600">Finding nearby places...</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {!loading && !error && nearbyPlaces.length === 0 && (
          <div className="text-center py-8">
            <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600">No nearby places found</p>
            <p className="text-sm text-gray-500 mt-1">
              Try enabling more place categories in admin settings
            </p>
          </div>
        )}

        {!loading && nearbyPlaces.length > 0 && (
          <div className="space-y-4">
            {nearbyPlaces.map((category) => (
              <div key={category.name} className="border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => toggleCategory(category.name)}
                  className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{category.icon}</span>
                    <div className="text-left">
                      <h4 className="font-medium text-gray-900">{category.displayName}</h4>
                      <p className="text-sm text-gray-600">{category.places.length} places found</p>
                    </div>
                  </div>
                  {expandedCategories.has(category.name) ? (
                    <ChevronUp className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </button>

                {expandedCategories.has(category.name) && (
                  <div className="divide-y divide-gray-200">
                    {category.places.map((place) => (
                      <div key={place.place_id} className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h5 className="font-medium text-gray-900">{place.name}</h5>
                            <p className="text-sm text-gray-600 mt-1">{place.vicinity}</p>
                            
                            <div className="flex items-center space-x-4 mt-2">
                              {place.rating && (
                                <div className="flex items-center space-x-1">
                                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                                  <span className="text-sm font-medium">{place.rating}</span>
                                  {place.user_ratings_total && (
                                    <span className="text-xs text-gray-500">({place.user_ratings_total})</span>
                                  )}
                                </div>
                              )}
                              
                              {place.opening_hours && (
                                <div className="flex items-center space-x-1">
                                  <Clock className="h-4 w-4 text-gray-400" />
                                  <span className={`text-xs font-medium ${
                                    place.opening_hours.open_now ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {place.opening_hours.open_now ? 'Open' : 'Closed'}
                                  </span>
                                </div>
                              )}
                              
                              {renderPriceLevel(place.price_level)}
                            </div>
                          </div>
                          
                          <div className="text-right ml-3">
                            <span className="text-sm font-medium text-blue-600">
                              {calculateDistance(place)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
