'use client';

import React, { useState } from 'react';
import { Star, MapPin, Clock, Phone, Globe, ExternalLink, ChevronDown, ChevronUp } from 'lucide-react';

interface NearbyPlace {
  place_id: string;
  name: string;
  vicinity: string;
  rating?: number;
  user_ratings_total?: number;
  price_level?: number;
  types: string[];
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  photos?: Array<{
    photo_reference: string;
    width: number;
    height: number;
  }>;
  opening_hours?: {
    open_now: boolean;
  };
  business_status?: string;
}

interface PlaceCategory {
  id: number;
  name: string;
  displayName: string;
  icon: string;
  color: string;
}

interface NearbyPlacesSection {
  category: PlaceCategory;
  places: NearbyPlace[];
  error?: string;
}

interface NearbyPlacesProps {
  nearbyPlaces: Record<string, NearbyPlacesSection>;
  className?: string;
}

const PlaceCard: React.FC<{ place: NearbyPlace }> = ({ place }) => {
  const getPhotoUrl = (photoReference: string) => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    if (!apiKey || !photoReference) return null;
    return `https://maps.googleapis.com/maps/api/place/photo?maxwidth=300&photo_reference=${photoReference}&key=${apiKey}`;
  };

  const getPriceLevelText = (level?: number) => {
    if (!level) return null;
    return '$'.repeat(level);
  };

  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(place.name)}&query_place_id=${place.place_id}`;
    window.open(url, '_blank');
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
      <div className="flex">
        {/* Photo */}
        <div className="w-24 h-24 flex-shrink-0">
          {place.photos && place.photos.length > 0 ? (
            <img
              src={getPhotoUrl(place.photos[0].photo_reference) || '/placeholder-business.jpg'}
              alt={place.name}
              className="w-full h-full object-cover rounded-l-lg"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-business.jpg';
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-100 rounded-l-lg flex items-center justify-center">
              <MapPin className="h-8 w-8 text-gray-400" />
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 text-sm leading-tight mb-1">
                {place.name}
              </h3>
              
              {/* Rating */}
              {place.rating && (
                <div className="flex items-center space-x-1 mb-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-3 w-3 ${
                          i < Math.floor(place.rating!)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-600">
                    {place.rating.toFixed(1)}
                  </span>
                  {place.user_ratings_total && (
                    <span className="text-xs text-gray-500">
                      ({place.user_ratings_total})
                    </span>
                  )}
                </div>
              )}

              {/* Address */}
              <p className="text-xs text-gray-600 mb-2">{place.vicinity}</p>

              {/* Status and Price */}
              <div className="flex items-center space-x-2">
                {place.opening_hours && (
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    place.opening_hours.open_now
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {place.opening_hours.open_now ? 'Open' : 'Closed'}
                  </span>
                )}
                
                {place.price_level && (
                  <span className="text-xs text-gray-600">
                    {getPriceLevelText(place.price_level)}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <button
              onClick={openInGoogleMaps}
              className="ml-2 p-1 text-gray-400 hover:text-blue-600 transition-colors"
              title="View on Google Maps"
            >
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const CategorySection: React.FC<{ section: NearbyPlacesSection }> = ({ section }) => {
  const [isExpanded, setIsExpanded] = useState(true);

  if (section.error) {
    return (
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-lg">{section.category.icon}</span>
          <h2 className="text-lg font-semibold text-gray-900">
            {section.category.displayName}
          </h2>
        </div>
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">Error loading places: {section.error}</p>
        </div>
      </div>
    );
  }

  if (section.places.length === 0) {
    return (
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-lg">{section.category.icon}</span>
          <h2 className="text-lg font-semibold text-gray-900">
            {section.category.displayName}
          </h2>
        </div>
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-sm text-gray-600">No places found in this category</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full mb-3 group"
      >
        <div className="flex items-center space-x-2">
          <span className="text-lg">{section.category.icon}</span>
          <h2 className="text-lg font-semibold text-gray-900">
            {section.category.displayName}
          </h2>
          <span 
            className="px-2 py-1 text-xs rounded-full text-white"
            style={{ backgroundColor: section.category.color }}
          >
            {section.places.length}
          </span>
        </div>
        {isExpanded ? (
          <ChevronUp className="h-5 w-5 text-gray-400 group-hover:text-gray-600" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-400 group-hover:text-gray-600" />
        )}
      </button>

      {isExpanded && (
        <div className="space-y-3">
          {section.places.map((place) => (
            <PlaceCard key={place.place_id} place={place} />
          ))}
        </div>
      )}
    </div>
  );
};

export const NearbyPlaces: React.FC<NearbyPlacesProps> = ({ nearbyPlaces, className = '' }) => {
  const sections = Object.values(nearbyPlaces);

  if (sections.length === 0) {
    return (
      <div className={`p-6 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
        <div className="text-center">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Nearby Places</h3>
          <p className="text-gray-600">
            Set property coordinates and generate nearby places to see what's around this location.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-2">What's Nearby</h2>
        <p className="text-gray-600">
          Discover restaurants, schools, shopping, and more around this property.
        </p>
      </div>

      {sections.map((section) => (
        <CategorySection key={section.category.name} section={section} />
      ))}
    </div>
  );
};
