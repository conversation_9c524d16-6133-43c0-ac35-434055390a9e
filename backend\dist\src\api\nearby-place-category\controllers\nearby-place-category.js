'use strict';
/**
 * nearby-place-category controller
 */
module.exports = {
    // Find all categories
    async find(ctx) {
        try {
            const categories = await strapi.entityService.findMany('api::nearby-place-category.nearby-place-category', {
                ...ctx.query,
            });
            return ctx.send({
                data: categories,
            });
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    // Find one category
    async findOne(ctx) {
        try {
            const { id } = ctx.params;
            const category = await strapi.entityService.findOne('api::nearby-place-category.nearby-place-category', id, {
                ...ctx.query,
            });
            return ctx.send({
                data: category,
            });
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    // Create category
    async create(ctx) {
        try {
            const category = await strapi.entityService.create('api::nearby-place-category.nearby-place-category', {
                data: ctx.request.body.data,
            });
            return ctx.send({
                data: category,
            });
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    // Update category
    async update(ctx) {
        try {
            const { id } = ctx.params;
            const category = await strapi.entityService.update('api::nearby-place-category.nearby-place-category', id, {
                data: ctx.request.body.data,
            });
            return ctx.send({
                data: category,
            });
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    // Delete category
    async delete(ctx) {
        try {
            const { id } = ctx.params;
            const category = await strapi.entityService.delete('api::nearby-place-category.nearby-place-category', id);
            return ctx.send({
                data: category,
            });
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    // Get enabled categories
    async getEnabled(ctx) {
        try {
            const enabledCategories = await strapi.entityService.findMany('api::nearby-place-category.nearby-place-category', {
                filters: { enabled: true },
                sort: { priority: 'desc' },
            });
            return ctx.send({
                data: enabledCategories,
            });
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    // Get Google Place Types
    async getGooglePlaceTypes(ctx) {
        try {
            const { GOOGLE_PLACE_TYPES } = require('../services/google-place-types');
            return ctx.send({
                data: GOOGLE_PLACE_TYPES,
            });
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
};
