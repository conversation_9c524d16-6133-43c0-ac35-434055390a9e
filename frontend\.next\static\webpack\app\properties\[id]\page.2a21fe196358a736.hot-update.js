"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/properties/[id]/page",{

/***/ "(app-pages-browser)/./src/app/properties/[id]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/properties/[id]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout/Layout */ \"(app-pages-browser)/./src/components/Layout/Layout.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_NearbyPlaces__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NearbyPlaces */ \"(app-pages-browser)/./src/components/NearbyPlaces.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Bath,Bed,Car,ChevronLeft,ChevronRight,Eye,Heart,Mail,MapPin,Phone,Share2,Square,Star,User,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PropertyDetailPage = ()=>{\n    var _property_images;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showImageModal, setShowImageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [nearbyPlaces, setNearbyPlaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingNearbyPlaces, setLoadingNearbyPlaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyDetailPage.useEffect\": ()=>{\n            if (params.id) {\n                fetchProperty(params.id);\n            }\n        }\n    }[\"PropertyDetailPage.useEffect\"], [\n        params.id\n    ]);\n    const fetchProperty = async (id)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.propertiesAPI.getById(id, {\n                populate: [\n                    'images',\n                    'floorPlan',\n                    'owner',\n                    'agent'\n                ]\n            });\n            setProperty(response.data);\n            // Fetch nearby places if they exist\n            if (response.data.nearbyPlaces) {\n                setNearbyPlaces(response.data.nearbyPlaces);\n            }\n        } catch (err) {\n            setError('Failed to fetch property details');\n            console.error('Error fetching property:', err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const generateNearbyPlaces = async ()=>{\n        if (!(property === null || property === void 0 ? void 0 : property.id) || !property.coordinates) {\n            alert('Property must have coordinates to generate nearby places');\n            return;\n        }\n        setLoadingNearbyPlaces(true);\n        try {\n            const response = await fetch(\"\".concat(\"http://localhost:1337\", \"/api/properties/\").concat(property.id, \"/generate-nearby-places\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setNearbyPlaces(data.data.nearbyPlaces);\n            } else {\n                var _errorData_error;\n                const errorData = await response.json();\n                alert(\"Failed to generate nearby places: \".concat(((_errorData_error = errorData.error) === null || _errorData_error === void 0 ? void 0 : _errorData_error.message) || 'Unknown error'));\n            }\n        } catch (error) {\n            console.error('Error generating nearby places:', error);\n            alert('Failed to generate nearby places');\n        } finally{\n            setLoadingNearbyPlaces(false);\n        }\n    };\n    const getImageUrl = (image)=>{\n        if (image === null || image === void 0 ? void 0 : image.url) {\n            return image.url.startsWith('http') ? image.url : \"http://localhost:1337\".concat(image.url);\n        }\n        return '/api/placeholder/800/600';\n    };\n    const formatPrice = (price, currency)=>{\n        return \"\".concat(currency, \" \").concat(price.toLocaleString());\n    };\n    const nextImage = ()=>{\n        if ((property === null || property === void 0 ? void 0 : property.images) && property.images.length > 0) {\n            setCurrentImageIndex((prev)=>prev === property.images.length - 1 ? 0 : prev + 1);\n        }\n    };\n    const prevImage = ()=>{\n        if ((property === null || property === void 0 ? void 0 : property.images) && property.images.length > 0) {\n            setCurrentImageIndex((prev)=>prev === 0 ? property.images.length - 1 : prev - 1);\n        }\n    };\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: property === null || property === void 0 ? void 0 : property.title,\n                    text: property === null || property === void 0 ? void 0 : property.description,\n                    url: window.location.href\n                });\n            } catch (err) {\n                console.log('Error sharing:', err);\n            }\n        } else {\n            // Fallback: copy to clipboard\n            navigator.clipboard.writeText(window.location.href);\n            alert('Link copied to clipboard!');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 bg-gray-300 rounded w-32 mb-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-96 bg-gray-300 rounded-lg mb-8\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"lg:col-span-2 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-300 rounded w-3/4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-gray-300 rounded w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-300 rounded w-3/4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-8 bg-gray-300 rounded mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 bg-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error || !property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Property Not Found\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: error || 'The property you are looking for does not exist.'\n                        }, void 0, false, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/properties'),\n                            className: \"bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"Back to Properties\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.back(),\n                            className: \"flex items-center text-gray-600 hover:text-gray-900 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Back to Properties\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-96 lg:h-[500px] rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: getImageUrl((_property_images = property.images) === null || _property_images === void 0 ? void 0 : _property_images[currentImageIndex]),\n                                            alt: property.title,\n                                            className: \"w-full h-full object-cover cursor-pointer\",\n                                            onClick: ()=>setShowImageModal(true)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        property.images && property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: prevImage,\n                                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: nextImage,\n                                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-6 w-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2\",\n                                                    children: property.images.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setCurrentImageIndex(index),\n                                                            className: \"w-3 h-3 rounded-full transition-all \".concat(index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50')\n                                                        }, index, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white px-3 py-1 rounded-full text-sm font-semibold capitalize \".concat(property.status === 'for-sale' ? 'bg-green-600' : property.status === 'for-rent' ? 'bg-blue-600' : property.status === 'sold' ? 'bg-gray-600' : property.status === 'rented' ? 'bg-purple-600' : 'bg-orange-600'),\n                                                    children: property.status.replace('-', ' ')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                property.isLuxury && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-yellow-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Luxury\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsFavorite(!isFavorite),\n                                                    className: \"p-2 rounded-full transition-all \".concat(isFavorite ? 'bg-red-600 text-white' : 'bg-white bg-opacity-90 text-gray-700 hover:bg-opacity-100'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 \".concat(isFavorite ? 'fill-current' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleShare,\n                                                    className: \"p-2 rounded-full bg-white bg-opacity-90 text-gray-700 hover:bg-opacity-100 transition-all\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: [\n                                                        property.views || 0,\n                                                        \" views\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, undefined),\n                                property.images && property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2 mt-4 overflow-x-auto pb-2\",\n                                    children: property.images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentImageIndex(index),\n                                            className: \"flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all \".concat(index === currentImageIndex ? 'border-blue-600' : 'border-transparent'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: getImageUrl(image),\n                                                alt: \"\".concat(property.title, \" \").concat(index + 1),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2 space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                                                    children: property.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-gray-600 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 350,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                property.address,\n                                                                                property.neighborhood && \", \".concat(property.neighborhood),\n                                                                                \", \",\n                                                                                property.city,\n                                                                                \", \",\n                                                                                property.country\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                property.propertyCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Property Code: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: [\n                                                                                \"#\",\n                                                                                property.propertyCode\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 40\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-blue-600 mb-1\",\n                                                                    children: formatPrice(property.price, property.currency)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500 capitalize\",\n                                                                    children: property.propertyType.replace('-', ' ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 py-6 border-t border-b border-gray-200\",\n                                                    children: [\n                                                        property.bedrooms > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                                    children: property.bedrooms\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Bedrooms\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        property.bathrooms > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                                    children: property.bathrooms\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Bathrooms\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        property.area > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                                    children: property.area\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: property.areaUnit || 'sq ft'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        property.parking && property.parking > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-8 w-8 text-blue-600 mx-auto mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                                    children: property.parking\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"Parking\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                property.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-gray-900 mb-3\",\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 leading-relaxed whitespace-pre-line\",\n                                                            children: property.description\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        property.features && property.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Features & Amenities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-3 gap-3\",\n                                                    children: property.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-blue-600 rounded-full mr-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"capitalize\",\n                                                                    children: feature.replace('-', ' ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Property Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        property.yearBuilt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Year Built\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: property.yearBuilt\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Property Type\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold capitalize\",\n                                                                    children: property.propertyType.replace('-', ' ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold capitalize\",\n                                                                    children: property.status.replace('-', ' ')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        property.furnished !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Furnished\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: property.furnished ? 'Yes' : 'No'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        property.petFriendly !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Pet Friendly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: property.petFriendly ? 'Yes' : 'No'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Listed\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: new Date(property.createdAt).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Contact Agent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                property.agent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold text-gray-900\",\n                                                                            children: property.agent.name || 'Real Estate Agent'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: property.agent.title || 'Licensed Agent'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                property.agent.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"tel:\".concat(property.agent.phone),\n                                                                    className: \"flex items-center w-full bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Call Agent\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                property.agent.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"mailto:\".concat(property.agent.email, \"?subject=Inquiry about \").concat(property.title),\n                                                                    className: \"flex items-center w-full bg-gray-100 text-gray-900 px-4 py-3 rounded-md hover:bg-gray-200 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Send Email\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-6 w-6 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold text-gray-900\",\n                                                                            children: \"Contact Us\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"For more information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center w-full bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Request Info\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"flex items-center w-full bg-gray-100 text-gray-900 px-4 py-3 rounded-md hover:bg-gray-200 transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-5 w-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Schedule Viewing\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                    children: \"Quick Stats\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Property ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        \"#\",\n                                                                        property.propertyCode || property.documentId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Views\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: property.views || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Listed\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: new Date(property.createdAt).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        property.updatedAt !== property.createdAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"Updated\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: new Date(property.updatedAt).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"What's Nearby\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        property.coordinates && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: generateNearbyPlaces,\n                                            disabled: loadingNearbyPlaces,\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: loadingNearbyPlaces ? 'Generating...' : nearbyPlaces ? 'Refresh Places' : 'Generate Nearby Places'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, undefined),\n                                !property.coordinates ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Property coordinates are required to show nearby places\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, undefined) : nearbyPlaces ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NearbyPlaces__WEBPACK_IMPORTED_MODULE_5__.NearbyPlaces, {\n                                    nearbyPlaces: nearbyPlaces,\n                                    showHeader: false\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-12 w-12 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Discover restaurants, schools, shopping, and more around this property\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: generateNearbyPlaces,\n                                            disabled: loadingNearbyPlaces,\n                                            className: \"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                                            children: loadingNearbyPlaces ? 'Generating...' : 'Generate Nearby Places'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined),\n                showImageModal && property.images && property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-4xl max-h-full p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowImageModal(false),\n                                className: \"absolute top-4 right-4 text-white hover:text-gray-300 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-8 w-8\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: getImageUrl(property.images[currentImageIndex]),\n                                alt: property.title,\n                                className: \"max-w-full max-h-full object-contain\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 15\n                            }, undefined),\n                            property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: prevImage,\n                                        className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-12 w-12\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: nextImage,\n                                        className: \"absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Bath_Bed_Car_ChevronLeft_ChevronRight_Eye_Heart_Mail_MapPin_Phone_Share2_Square_Star_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-12 w-12\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 605,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 220,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\3-Development Web\\\\real estate\\\\frontend\\\\src\\\\app\\\\properties\\\\[id]\\\\page.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PropertyDetailPage, \"dLxjDTjuXvFp5jufxkkppFl2GLA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = PropertyDetailPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PropertyDetailPage);\nvar _c;\n$RefreshReg$(_c, \"PropertyDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/properties/[id]/page.tsx\n"));

/***/ })

});