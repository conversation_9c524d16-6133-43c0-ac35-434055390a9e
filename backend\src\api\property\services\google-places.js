'use strict';

const axios = require('axios');

/**
 * Google Places service for finding nearby places
 */

module.exports = () => ({
  /**
   * Find nearby places using Google Places API
   * @param {Object} params - Search parameters
   * @param {number} params.lat - Latitude
   * @param {number} params.lng - Longitude
   * @param {Array} params.types - Google place types to search for
   * @param {number} params.radius - Search radius in meters (default: 1000)
   * @param {number} params.maxResults - Maximum results to return (default: 10)
   * @returns {Promise<Array>} Array of nearby places
   */
  async findNearbyPlaces({ lat, lng, types, radius = 1000, maxResults = 10 }) {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      throw new Error('Google Maps API key not configured');
    }

    if (!lat || !lng) {
      throw new Error('Latitude and longitude are required');
    }

    if (!types || !Array.isArray(types) || types.length === 0) {
      throw new Error('Place types are required');
    }

    try {
      const places = [];
      
      // Search for each place type
      for (const type of types) {
        const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
        const params = {
          location: `${lat},${lng}`,
          radius: radius,
          type: type,
          key: apiKey
        };

        const response = await axios.get(url, { params });
        
        if (response.data.status === 'OK') {
          const typePlaces = response.data.results.slice(0, maxResults).map(place => ({
            place_id: place.place_id,
            name: place.name,
            vicinity: place.vicinity,
            rating: place.rating || null,
            user_ratings_total: place.user_ratings_total || 0,
            price_level: place.price_level || null,
            types: place.types,
            geometry: {
              location: place.geometry.location
            },
            photos: place.photos ? place.photos.slice(0, 1).map(photo => ({
              photo_reference: photo.photo_reference,
              width: photo.width,
              height: photo.height
            })) : [],
            opening_hours: place.opening_hours ? {
              open_now: place.opening_hours.open_now
            } : null,
            business_status: place.business_status || null
          }));
          
          places.push(...typePlaces);
        }
      }

      // Remove duplicates based on place_id
      const uniquePlaces = places.filter((place, index, self) => 
        index === self.findIndex(p => p.place_id === place.place_id)
      );

      // Sort by rating and user ratings
      uniquePlaces.sort((a, b) => {
        const aScore = (a.rating || 0) * Math.log(a.user_ratings_total + 1);
        const bScore = (b.rating || 0) * Math.log(b.user_ratings_total + 1);
        return bScore - aScore;
      });

      return uniquePlaces.slice(0, maxResults);
    } catch (error) {
      console.error('Google Places API error:', error.message);
      throw new Error(`Failed to fetch nearby places: ${error.message}`);
    }
  },

  /**
   * Get photo URL for a place photo
   * @param {string} photoReference - Photo reference from Google Places
   * @param {number} maxWidth - Maximum width (default: 400)
   * @returns {string} Photo URL
   */
  getPhotoUrl(photoReference, maxWidth = 400) {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    
    if (!apiKey || !photoReference) {
      return null;
    }

    return `https://maps.googleapis.com/maps/api/place/photo?maxwidth=${maxWidth}&photo_reference=${photoReference}&key=${apiKey}`;
  },

  /**
   * Get place details
   * @param {string} placeId - Google Place ID
   * @returns {Promise<Object>} Place details
   */
  async getPlaceDetails(placeId) {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      throw new Error('Google Maps API key not configured');
    }

    try {
      const url = 'https://maps.googleapis.com/maps/api/place/details/json';
      const params = {
        place_id: placeId,
        fields: 'name,rating,formatted_phone_number,formatted_address,website,opening_hours,photos,reviews',
        key: apiKey
      };

      const response = await axios.get(url, { params });
      
      if (response.data.status === 'OK') {
        return response.data.result;
      } else {
        throw new Error(`Google Places API error: ${response.data.status}`);
      }
    } catch (error) {
      console.error('Google Place Details API error:', error.message);
      throw new Error(`Failed to fetch place details: ${error.message}`);
    }
  }
});
