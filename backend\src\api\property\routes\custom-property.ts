/**
 * Custom property routes
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/properties/my-properties',
      handler: 'api::property.property.getMyProperties',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/properties/:id/edit',
      handler: 'api::property.property.getForEdit',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/properties/:id/publish',
      handler: 'api::property.property.publish',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/properties/:id/unpublish',
      handler: 'api::property.property.unpublish',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/properties/:id/generate-nearby-places',
      handler: 'property.generateNearbyPlaces',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/properties/:id/nearby-places',
      handler: 'property.getNearbyPlaces',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
