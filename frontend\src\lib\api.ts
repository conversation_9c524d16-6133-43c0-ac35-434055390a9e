import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337';

// Create axios instance
export const api = axios.create({
  baseURL: `${API_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('jwt');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('jwt');
      localStorage.removeItem('user');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (identifier: string, password: string) => {
    const response = await api.post('/auth/local', {
      identifier,
      password,
    });
    return response.data;
  },

  register: async (username: string, email: string, password: string) => {
    const response = await api.post('/auth/local/register', {
      username,
      email,
      password,
    });
    return response.data;
  },

  me: async () => {
    const response = await api.get('/users/me');
    return response.data;
  },

  forgotPassword: async (email: string) => {
    const response = await api.post('/auth/forgot-password', {
      email,
    });
    return response.data;
  },

  resetPassword: async (code: string, password: string, passwordConfirmation: string) => {
    const response = await api.post('/auth/reset-password', {
      code,
      password,
      passwordConfirmation,
    });
    return response.data;
  },
};

// Properties API
export const propertiesAPI = {
  getAll: async (params?: any) => {
    const response = await api.get('/properties', {
      params: {
        populate: ['images', 'owner', 'agent', 'project'],
        ...params
      }
    });
    return response.data;
  },

  getById: async (id: string, params?: any) => {
    const response = await api.get(`/properties/${id}`, {
      params: {
        populate: ['images', 'floorPlan', 'owner', 'agent', 'project'],
        ...params
      }
    });
    return response.data;
  },

  getForEdit: async (id: string) => {
    const response = await api.get(`/properties/${id}/edit`);
    return response.data;
  },

  getFeatured: async () => {
    const response = await api.get('/properties', {
      params: {
        populate: ['images', 'owner', 'agent', 'project'],
        filters: {
          featured: true,
          publishedAt: { $notNull: true }
        },
        sort: ['createdAt:desc'],
        pagination: {
          limit: 6
        }
      }
    });
    return response.data;
  },

  search: async (searchParams: any) => {
    const filters: any = {};

    if (searchParams.location) {
      filters.$or = [
        { city: { $containsi: searchParams.location } },
        { address: { $containsi: searchParams.location } },
        { neighborhood: { $containsi: searchParams.location } }
      ];
    }

    if (searchParams.propertyType) {
      filters.propertyType = searchParams.propertyType;
    }

    if (searchParams.offerType) {
      filters.status = searchParams.offerType;
    }

    if (searchParams.priceMin) {
      filters.price = { $gte: parseFloat(searchParams.priceMin) };
    }

    if (searchParams.priceMax) {
      filters.price = { ...filters.price, $lte: parseFloat(searchParams.priceMax) };
    }

    if (searchParams.bedrooms) {
      filters.bedrooms = { $gte: parseInt(searchParams.bedrooms) };
    }

    if (searchParams.bathrooms) {
      filters.bathrooms = { $gte: parseInt(searchParams.bathrooms) };
    }

    if (searchParams.area) {
      filters.area = { $gte: parseFloat(searchParams.area) };
    }

    if (searchParams.city) {
      filters.city = { $containsi: searchParams.city };
    }

    if (searchParams.neighborhood) {
      filters.neighborhood = { $containsi: searchParams.neighborhood };
    }

    if (searchParams.propertyCode) {
      filters.propertyCode = { $containsi: searchParams.propertyCode };
    }

    if (searchParams.isLuxury) {
      filters.isLuxury = true;
    }

    const response = await api.get('/properties', {
      params: {
        populate: ['images', 'owner', 'agent', 'project'],
        filters,
        sort: ['createdAt:desc']
      }
    });
    return response.data;
  },

  getOne: async (id: string) => {
    const response = await api.get(`/properties/${id}`, {
      params: {
        populate: ['images', 'owner', 'agent', 'project', 'floorPlan']
      }
    });
    return response.data;
  },

  create: async (propertyData: any) => {
    const response = await api.post('/properties', {
      data: propertyData
    });
    return response.data;
  },

  update: async (id: string, propertyData: any) => {
    const response = await api.put(`/properties/${id}`, {
      data: propertyData
    });
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/properties/${id}`);
    return response.data;
  },

  publish: async (id: string) => {
    const response = await api.put(`/properties/${id}`, {
      data: {
        publishedAt: new Date().toISOString()
      }
    });
    return response.data;
  },

  unpublish: async (id: string) => {
    const response = await api.put(`/properties/${id}`, {
      data: {
        publishedAt: null
      }
    });
    return response.data;
  },

  getMyProperties: async () => {
    try {
      const response = await api.get('/properties/my-properties');
      return response.data.data || response.data;
    } catch (error) {
      console.error('Error fetching my properties:', error);
      throw error;
    }
  },

  getCities: async () => {
    const response = await api.get('/properties', {
      params: {
        fields: ['city'],
        pagination: { limit: -1 }
      }
    });
    const cities = [...new Set(response.data.data.map((p: any) => p.city))];
    return cities;
  },

  getNeighborhoods: async (city?: string) => {
    const filters = city ? { city: { $eq: city } } : {};
    const response = await api.get('/properties', {
      params: {
        fields: ['neighborhood'],
        filters,
        pagination: { limit: -1 }
      }
    });
    const neighborhoods = [...new Set(response.data.data.map((p: any) => p.neighborhood).filter(Boolean))];
    return neighborhoods;
  },
};

// Projects API
export const projectsAPI = {
  getAll: async (params?: any) => {
    const response = await api.get('/projects', {
      params: {
        populate: ['images', 'properties'],
        ...params
      }
    });
    return response.data;
  },

  getFeatured: async () => {
    const response = await api.get('/projects', {
      params: {
        populate: ['images', 'properties'],
        filters: {
          featured: true,
          publishedAt: { $notNull: true }
        },
        sort: ['createdAt:desc'],
        pagination: {
          limit: 4
        }
      }
    });
    return response.data;
  },

  getOne: async (id: string) => {
    const response = await api.get(`/projects/${id}`, {
      params: {
        populate: ['images', 'properties', 'floorPlans', 'brochure']
      }
    });
    return response.data;
  },

  getProperties: async (id: string) => {
    const response = await api.get(`/projects/${id}/properties`);
    return response.data;
  },
};

// Messages API
export const messagesAPI = {
  getAll: async () => {
    const response = await api.get('/messages');
    return response.data;
  },

  getOne: async (id: string) => {
    const response = await api.get(`/messages/${id}`);
    return response.data;
  },

  create: async (data: any) => {
    const response = await api.post('/messages', { data });
    return response.data;
  },

  getInbox: async () => {
    const response = await api.get('/messages/inbox');
    return response.data;
  },

  getSent: async () => {
    const response = await api.get('/messages/sent');
    return response.data;
  },

  markAsRead: async (id: string) => {
    const response = await api.put(`/messages/${id}/mark-as-read`);
    return response.data;
  },
};

// Membership API
export const membershipAPI = {
  getAll: async () => {
    const response = await api.get('/memberships');
    return response.data;
  },

  getMyMembership: async () => {
    const response = await api.get('/memberships/my-membership');
    return response.data;
  },

  subscribe: async (membershipId: string) => {
    const response = await api.post('/memberships/subscribe', {
      membershipId
    });
    return response.data;
  },
};

// Upload API
export const uploadAPI = {
  upload: async (files: FileList) => {
    const formData = new FormData();
    Array.from(files).forEach((file) => {
      formData.append('files', file);
    });

    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};
